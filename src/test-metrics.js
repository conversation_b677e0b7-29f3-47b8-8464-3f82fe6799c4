// Test script to send sample metrics to the mock endpoint
import dotenv from "dotenv";

dotenv.config();

const host = 'localhost';
const port = 3000;
const metricsEndpoint = `http://${host}:${port}/api/metrics`;

// Sample metrics payload for testing
const sampleMetrics = {
  event: "pull_request.opened",
  timestamp: new Date().toISOString(),
  repository: {
    owner: "test-org",
    name: "test-repo",
    fullName: "test-org/test-repo",
    id: 123456789
  },
  pullRequest: {
    number: 42,
    title: "Add new authentication feature",
    author: "developer123",
    authorId: 987654321,
    state: "open",
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-15T11:45:00Z",
    mergedAt: null,
    mergedBy: null,
    baseBranch: "main",
    headBranch: "feature/auth",
    url: "https://github.com/test-org/test-repo/pull/42"
  },
  metrics: {
    commits: 5,
    filesChanged: 12,
    additions: 245,
    deletions: 67,
    totalChanges: 312,
    reviews: {
      total: 2,
      approved: 1,
      changesRequested: 0
    },
    comments: 8,
    fileTypes: ["js", "ts", "json", "md"],
    largestFile: {
      filename: "src/auth.js",
      changes: 89,
      additions: 76,
      deletions: 13
    }
  }
};

// Sample merged PR metrics
const sampleMergedMetrics = {
  ...sampleMetrics,
  event: "pull_request.merged",
  pullRequest: {
    ...sampleMetrics.pullRequest,
    state: "closed",
    mergedAt: "2024-01-16T14:20:00Z",
    mergedBy: "maintainer456"
  },
  metrics: {
    ...sampleMetrics.metrics,
    reviews: {
      total: 3,
      approved: 2,
      changesRequested: 1
    },
    comments: 12
  }
};

async function testMetricsEndpoint() {
  console.log('🧪 Testing Mock Metrics Endpoint...\n');
  
  try {
    // Test 1: Send opened PR metrics
    console.log('📤 Sending opened PR metrics...');
    const response1 = await fetch(metricsEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(sampleMetrics)
    });

    if (response1.ok) {
      const result1 = await response1.json();
      console.log('✅ Response:', result1);
    } else {
      console.error('❌ Failed:', response1.status, response1.statusText);
    }

    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test 2: Send merged PR metrics
    console.log('\n📤 Sending merged PR metrics...');
    const response2 = await fetch(metricsEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(sampleMergedMetrics)
    });

    if (response2.ok) {
      const result2 = await response2.json();
      console.log('✅ Response:', result2);
    } else {
      console.error('❌ Failed:', response2.status, response2.statusText);
    }

    console.log('\n🎉 Test completed! Check the server console for detailed metrics output.');

  } catch (error) {
    console.error('❌ Error testing metrics endpoint:', error.message);
    console.log('\n💡 Make sure the server is running with: npm start');
  }
}

// Test invalid payload
async function testInvalidPayload() {
  console.log('\n🧪 Testing invalid payload handling...');
  
  try {
    const response = await fetch(metricsEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: 'invalid json'
    });

    const result = await response.json();
    console.log('Response:', result);
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Test wrong method
async function testWrongMethod() {
  console.log('\n🧪 Testing wrong HTTP method...');
  
  try {
    const response = await fetch(metricsEndpoint, {
      method: 'GET'
    });

    const result = await response.json();
    console.log('Response:', result);
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Run all tests
async function runAllTests() {
  await testMetricsEndpoint();
  await testInvalidPayload();
  await testWrongMethod();
}

// Check if this script is being run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(console.error);
}

export { testMetricsEndpoint, sampleMetrics, sampleMergedMetrics };
