// Example usage of the PR details fetching functionality
import dotenv from "dotenv";
import {App} from "octokit";
import fs from "fs";

// Load environment variables
dotenv.config();

// Initialize the GitHub App
const app = new App({
  appId: process.env.APP_ID,
  privateKey: fs.readFileSync(process.env.PRIVATE_KEY_PATH, "utf8"),
});

// GraphQL query to fetch comprehensive PR data in a single request
const PR_DETAILS_QUERY = `
  query($owner: String!, $repo: String!, $number: Int!) {
    repository(owner: $owner, name: $repo) {
      pullRequest(number: $number) {
        id
        number
        title
        body
        state
        createdAt
        updatedAt
        mergedAt
        closedAt
        merged
        mergeable
        url
        author {
          login
          ... on User {
            id
          }
        }
        mergedBy {
          login
        }
        baseRefName
        headRefName
        additions
        deletions
        changedFiles
        commits(first: 100) {
          totalCount
          nodes {
            commit {
              message
              author {
                name
                email
                date
              }
              additions
              deletions
              changedFiles
            }
          }
        }
        files(first: 100) {
          totalCount
          nodes {
            path
            additions
            deletions
            changeType
          }
        }
        reviews(first: 100) {
          totalCount
          nodes {
            id
            state
            author {
              login
            }
            createdAt
            submittedAt
          }
        }
        comments(first: 100) {
          totalCount
          nodes {
            id
            author {
              login
            }
            body
            createdAt
          }
        }
        reviewComments(first: 100) {
          totalCount
          nodes {
            id
            author {
              login
            }
            body
            createdAt
          }
        }
      }
    }
  }
`;

// Function to fetch detailed pull request information using GraphQL
async function fetchPullRequestDetails(octokit, owner, repo, pullNumber) {
  try {
    const response = await octokit.graphql(PR_DETAILS_QUERY, {
      owner,
      repo,
      number: pullNumber
    });

    const pr = response.repository.pullRequest;

    if (!pr) {
      throw new Error(`Pull request #${pullNumber} not found`);
    }

    // Extract file types from file paths
    const fileTypes = [...new Set(pr.files.nodes.map(file => {
      const ext = file.path.split('.').pop();
      return ext || 'no-extension';
    }))];

    // Find the largest file by total changes
    const largestFile = pr.files.nodes.reduce((largest, file) => {
      const changes = file.additions + file.deletions;
      return changes > (largest?.changes || 0) ? {
        filename: file.path,
        changes,
        additions: file.additions,
        deletions: file.deletions,
        changeType: file.changeType
      } : largest;
    }, null);

    // Calculate metrics
    const metrics = {
      totalCommits: pr.commits.totalCount,
      totalFiles: pr.changedFiles,
      totalAdditions: pr.additions,
      totalDeletions: pr.deletions,
      totalChanges: pr.additions + pr.deletions,
      totalReviews: pr.reviews.totalCount,
      totalComments: pr.comments.totalCount + pr.reviewComments.totalCount,
      approvedReviews: pr.reviews.nodes.filter(review => review.state === 'APPROVED').length,
      changesRequestedReviews: pr.reviews.nodes.filter(review => review.state === 'CHANGES_REQUESTED').length,
      fileTypes,
      largestFile
    };

    // Transform to match expected structure
    const pullRequest = {
      id: pr.id,
      number: pr.number,
      title: pr.title,
      body: pr.body,
      state: pr.state.toLowerCase(),
      created_at: pr.createdAt,
      updated_at: pr.updatedAt,
      merged_at: pr.mergedAt,
      closed_at: pr.closedAt,
      merged: pr.merged,
      mergeable: pr.mergeable,
      html_url: pr.url,
      user: {
        login: pr.author?.login || 'unknown',
        id: pr.author?.id || null
      },
      merged_by: pr.mergedBy ? { login: pr.mergedBy.login } : null,
      base: { ref: pr.baseRefName },
      head: { ref: pr.headRefName },
      additions: pr.additions,
      deletions: pr.deletions,
      changed_files: pr.changedFiles
    };

    return {
      pullRequest,
      commits: pr.commits.nodes,
      files: pr.files.nodes,
      reviews: pr.reviews.nodes,
      comments: [...pr.comments.nodes, ...pr.reviewComments.nodes],
      metrics
    };
  } catch (error) {
    console.error(`Error fetching PR details via GraphQL: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}, Message: ${error.response.data?.message || error.message}`);
    }
    throw error;
  }
}

// Example function to fetch PR details for a specific repository
async function exampleFetchPRDetails() {
  try {
    // Replace these with actual values
    const owner = "your-org";
    const repo = "your-repo";
    const pullNumber = 123;
    const installationId = 12345; // Your GitHub App installation ID
    
    console.log(`Fetching details for PR #${pullNumber} in ${owner}/${repo}...`);
    
    // Get an installation-specific octokit instance
    const octokit = await app.getInstallationOctokit(installationId);
    
    // Fetch the PR details
    const prDetails = await fetchPullRequestDetails(octokit, owner, repo, pullNumber);
    
    // Display the results
    console.log('\n=== PULL REQUEST DETAILS ===');
    console.log(`Title: ${prDetails.pullRequest.title}`);
    console.log(`Author: ${prDetails.pullRequest.user.login}`);
    console.log(`State: ${prDetails.pullRequest.state}`);
    console.log(`Created: ${prDetails.pullRequest.created_at}`);
    console.log(`Base branch: ${prDetails.pullRequest.base.ref}`);
    console.log(`Head branch: ${prDetails.pullRequest.head.ref}`);
    
    console.log('\n=== METRICS ===');
    console.log(`Total commits: ${prDetails.metrics.totalCommits}`);
    console.log(`Total files changed: ${prDetails.metrics.totalFiles}`);
    console.log(`Total additions: ${prDetails.metrics.totalAdditions}`);
    console.log(`Total deletions: ${prDetails.metrics.totalDeletions}`);
    console.log(`Total changes: ${prDetails.metrics.totalChanges}`);
    console.log(`Total reviews: ${prDetails.metrics.totalReviews}`);
    console.log(`Approved reviews: ${prDetails.metrics.approvedReviews}`);
    console.log(`Changes requested: ${prDetails.metrics.changesRequestedReviews}`);
    console.log(`Total comments: ${prDetails.metrics.totalComments}`);
    console.log(`File types: ${prDetails.metrics.fileTypes.join(', ')}`);
    
    if (prDetails.metrics.largestFile) {
      console.log(`Largest file: ${prDetails.metrics.largestFile.filename} (${prDetails.metrics.largestFile.changes} changes)`);
    }
    
    // You can also access individual arrays:
    console.log('\n=== COMMITS ===');
    prDetails.commits.forEach((commit, index) => {
      console.log(`${index + 1}. ${commit.commit.message.split('\n')[0]} (${commit.author?.login || 'Unknown'})`);
    });
    
    console.log('\n=== FILES CHANGED ===');
    prDetails.files.forEach(file => {
      console.log(`${file.filename}: +${file.additions} -${file.deletions} (${file.status})`);
    });
    
    return prDetails;
    
  } catch (error) {
    console.error('Error in example:', error.message);
  }
}

// Uncomment the line below to run the example
// exampleFetchPRDetails();

export { fetchPullRequestDetails, exampleFetchPRDetails };
