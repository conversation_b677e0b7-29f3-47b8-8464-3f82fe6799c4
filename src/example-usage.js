// Example usage of the PR details fetching functionality
import dotenv from "dotenv";
import {App} from "octokit";
import fs from "fs";

// Load environment variables
dotenv.config();

// Initialize the GitHub App
const app = new App({
  appId: process.env.APP_ID,
  privateKey: fs.readFileSync(process.env.PRIVATE_KEY_PATH, "utf8"),
});

// Import the fetchPullRequestDetails function from the main file
// (In a real scenario, you'd export this from index.js and import it here)

// Function to fetch detailed pull request information (copied from index.js)
async function fetchPullRequestDetails(octokit, owner, repo, pullNumber) {
  try {
    // Fetch the pull request details
    const { data: pullRequest } = await octokit.request("GET /repos/{owner}/{repo}/pulls/{pull_number}", {
      owner,
      repo,
      pull_number: pullNumber,
      headers: {
        "x-github-api-version": "2022-11-28",
      },
    });

    // Fetch additional metrics and details
    const [
      { data: commits },
      { data: files },
      { data: reviews },
      { data: comments }
    ] = await Promise.all([
      // Get commits in the PR
      octokit.request("GET /repos/{owner}/{repo}/pulls/{pull_number}/commits", {
        owner,
        repo,
        pull_number: pullNumber,
        headers: { "x-github-api-version": "2022-11-28" },
      }),
      // Get files changed in the PR
      octokit.request("GET /repos/{owner}/{repo}/pulls/{pull_number}/files", {
        owner,
        repo,
        pull_number: pullNumber,
        headers: { "x-github-api-version": "2022-11-28" },
      }),
      // Get reviews for the PR
      octokit.request("GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews", {
        owner,
        repo,
        pull_number: pullNumber,
        headers: { "x-github-api-version": "2022-11-28" },
      }),
      // Get comments on the PR
      octokit.request("GET /repos/{owner}/{repo}/issues/{issue_number}/comments", {
        owner,
        repo,
        issue_number: pullNumber,
        headers: { "x-github-api-version": "2022-11-28" },
      })
    ]);

    // Calculate metrics
    const metrics = {
      totalCommits: commits.length,
      totalFiles: files.length,
      totalAdditions: files.reduce((sum, file) => sum + file.additions, 0),
      totalDeletions: files.reduce((sum, file) => sum + file.deletions, 0),
      totalChanges: files.reduce((sum, file) => sum + file.changes, 0),
      totalReviews: reviews.length,
      totalComments: comments.length,
      approvedReviews: reviews.filter(review => review.state === 'APPROVED').length,
      changesRequestedReviews: reviews.filter(review => review.state === 'CHANGES_REQUESTED').length,
      fileTypes: [...new Set(files.map(file => {
        const ext = file.filename.split('.').pop();
        return ext || 'no-extension';
      }))],
      largestFile: files.reduce((largest, file) => 
        file.changes > (largest?.changes || 0) ? file : largest, null
      )
    };

    return {
      pullRequest,
      commits,
      files,
      reviews,
      comments,
      metrics
    };
  } catch (error) {
    console.error(`Error fetching PR details: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}, Message: ${error.response.data.message}`);
    }
    throw error;
  }
}

// Example function to fetch PR details for a specific repository
async function exampleFetchPRDetails() {
  try {
    // Replace these with actual values
    const owner = "your-org";
    const repo = "your-repo";
    const pullNumber = 123;
    const installationId = 12345; // Your GitHub App installation ID
    
    console.log(`Fetching details for PR #${pullNumber} in ${owner}/${repo}...`);
    
    // Get an installation-specific octokit instance
    const octokit = await app.getInstallationOctokit(installationId);
    
    // Fetch the PR details
    const prDetails = await fetchPullRequestDetails(octokit, owner, repo, pullNumber);
    
    // Display the results
    console.log('\n=== PULL REQUEST DETAILS ===');
    console.log(`Title: ${prDetails.pullRequest.title}`);
    console.log(`Author: ${prDetails.pullRequest.user.login}`);
    console.log(`State: ${prDetails.pullRequest.state}`);
    console.log(`Created: ${prDetails.pullRequest.created_at}`);
    console.log(`Base branch: ${prDetails.pullRequest.base.ref}`);
    console.log(`Head branch: ${prDetails.pullRequest.head.ref}`);
    
    console.log('\n=== METRICS ===');
    console.log(`Total commits: ${prDetails.metrics.totalCommits}`);
    console.log(`Total files changed: ${prDetails.metrics.totalFiles}`);
    console.log(`Total additions: ${prDetails.metrics.totalAdditions}`);
    console.log(`Total deletions: ${prDetails.metrics.totalDeletions}`);
    console.log(`Total changes: ${prDetails.metrics.totalChanges}`);
    console.log(`Total reviews: ${prDetails.metrics.totalReviews}`);
    console.log(`Approved reviews: ${prDetails.metrics.approvedReviews}`);
    console.log(`Changes requested: ${prDetails.metrics.changesRequestedReviews}`);
    console.log(`Total comments: ${prDetails.metrics.totalComments}`);
    console.log(`File types: ${prDetails.metrics.fileTypes.join(', ')}`);
    
    if (prDetails.metrics.largestFile) {
      console.log(`Largest file: ${prDetails.metrics.largestFile.filename} (${prDetails.metrics.largestFile.changes} changes)`);
    }
    
    // You can also access individual arrays:
    console.log('\n=== COMMITS ===');
    prDetails.commits.forEach((commit, index) => {
      console.log(`${index + 1}. ${commit.commit.message.split('\n')[0]} (${commit.author?.login || 'Unknown'})`);
    });
    
    console.log('\n=== FILES CHANGED ===');
    prDetails.files.forEach(file => {
      console.log(`${file.filename}: +${file.additions} -${file.deletions} (${file.status})`);
    });
    
    return prDetails;
    
  } catch (error) {
    console.error('Error in example:', error.message);
  }
}

// Uncomment the line below to run the example
// exampleFetchPRDetails();

export { fetchPullRequestDetails, exampleFetchPRDetails };
