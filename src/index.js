// These are the dependencies for this file.
//
// You installed the `dotenv` and `octokit` modules earlier. The `@octokit/webhooks` is a dependency of the `octokit` module, so you don't need to install it separately. The `fs` and `http` dependencies are built-in Node.js modules.
import dotenv from "dotenv";
import {App} from "octokit";
import {createNodeMiddleware} from "@octokit/webhooks";
import fs from "fs";
import http from "http";

// This reads your `.env` file and adds the variables from that file to the `process.env` object in Node.js.
dotenv.config();

// This assigns the values of your environment variables to local variables.
const appId = process.env.APP_ID;
const webhookSecret = process.env.WEBHOOK_SECRET;
const privateKeyPath = process.env.PRIVATE_KEY_PATH;

// This reads the contents of your private key file.
const privateKey = fs.readFileSync(privateKeyPath, "utf8");

// This creates a new instance of the Octokit App class.
const app = new App({
  appId: appId,
  privateKey: privateKey,
  webhooks: {
    secret: webhookSecret
  },
});

// This defines the message that your app will post to pull requests.
const messageForNewPRs = "Thanks for opening a new PR! Please follow our contributing guidelines to make your PR easier to review.";

// Function to get PR details for any repository (requires installation access)
async function getPRDetailsForRepo(owner, repo, pullNumber, installationId) {
  try {
    // Get an installation-specific octokit instance
    const octokit = await app.getInstallationOctokit(installationId);

    const prDetails = await fetchPullRequestDetails(octokit, owner, repo, pullNumber);

    console.log(`\n=== PR #${pullNumber} Details for ${owner}/${repo} ===`);
    console.log(`Title: ${prDetails.pullRequest.title}`);
    console.log(`Author: ${prDetails.pullRequest.user.login}`);
    console.log(`State: ${prDetails.pullRequest.state}`);
    console.log(`Created: ${prDetails.pullRequest.created_at}`);
    console.log(`Updated: ${prDetails.pullRequest.updated_at}`);

    if (prDetails.pullRequest.merged) {
      console.log(`Merged: ${prDetails.pullRequest.merged_at}`);
      console.log(`Merged by: ${prDetails.pullRequest.merged_by?.login || 'Unknown'}`);
    }

    console.log('\n=== Metrics ===');
    console.log(`Commits: ${prDetails.metrics.totalCommits}`);
    console.log(`Files: ${prDetails.metrics.totalFiles}`);
    console.log(`Additions: ${prDetails.metrics.totalAdditions}`);
    console.log(`Deletions: ${prDetails.metrics.totalDeletions}`);
    console.log(`Reviews: ${prDetails.metrics.totalReviews} (${prDetails.metrics.approvedReviews} approved, ${prDetails.metrics.changesRequestedReviews} changes requested)`);
    console.log(`Comments: ${prDetails.metrics.totalComments}`);
    console.log(`File types: ${prDetails.metrics.fileTypes.join(', ')}`);

    return prDetails;
  } catch (error) {
    console.error(`Error fetching PR details: ${error.message}`);
    throw error;
  }
}

// Function to fetch detailed pull request information
async function fetchPullRequestDetails(octokit, owner, repo, pullNumber) {
  try {
    // Fetch the pull request details
    const { data: pullRequest } = await octokit.request("GET /repos/{owner}/{repo}/pulls/{pull_number}", {
      owner,
      repo,
      pull_number: pullNumber,
      headers: {
        "x-github-api-version": "2022-11-28",
      },
    });

    // Fetch additional metrics and details
    const [
      { data: commits },
      { data: files },
      { data: reviews },
      { data: comments }
    ] = await Promise.all([
      // Get commits in the PR
      octokit.request("GET /repos/{owner}/{repo}/pulls/{pull_number}/commits", {
        owner,
        repo,
        pull_number: pullNumber,
        headers: { "x-github-api-version": "2022-11-28" },
      }),
      // Get files changed in the PR
      octokit.request("GET /repos/{owner}/{repo}/pulls/{pull_number}/files", {
        owner,
        repo,
        pull_number: pullNumber,
        headers: { "x-github-api-version": "2022-11-28" },
      }),
      // Get reviews for the PR
      octokit.request("GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews", {
        owner,
        repo,
        pull_number: pullNumber,
        headers: { "x-github-api-version": "2022-11-28" },
      }),
      // Get comments on the PR
      octokit.request("GET /repos/{owner}/{repo}/issues/{issue_number}/comments", {
        owner,
        repo,
        issue_number: pullNumber,
        headers: { "x-github-api-version": "2022-11-28" },
      })
    ]);

    // Calculate metrics
    const metrics = {
      totalCommits: commits.length,
      totalFiles: files.length,
      totalAdditions: files.reduce((sum, file) => sum + file.additions, 0),
      totalDeletions: files.reduce((sum, file) => sum + file.deletions, 0),
      totalChanges: files.reduce((sum, file) => sum + file.changes, 0),
      totalReviews: reviews.length,
      totalComments: comments.length,
      approvedReviews: reviews.filter(review => review.state === 'APPROVED').length,
      changesRequestedReviews: reviews.filter(review => review.state === 'CHANGES_REQUESTED').length,
      fileTypes: [...new Set(files.map(file => {
        const ext = file.filename.split('.').pop();
        return ext || 'no-extension';
      }))],
      largestFile: files.reduce((largest, file) =>
        file.changes > (largest?.changes || 0) ? file : largest, null
      )
    };

    return {
      pullRequest,
      commits,
      files,
      reviews,
      comments,
      metrics
    };
  } catch (error) {
    console.error(`Error fetching PR details: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}, Message: ${error.response.data.message}`);
    }
    throw error;
  }
}

// This adds an event handler that your code will call later. When this event handler is called, it will log the event to the console. Then, it will use GitHub's REST API to add a comment to the pull request that triggered the event.
async function handlePullRequestOpened({octokit, payload}) {
  console.log(`Received a pull request event for #${payload.pull_request.number}`);

  try {
    // Fetch detailed PR information
    const prDetails = await fetchPullRequestDetails(
      octokit,
      payload.repository.owner.login,
      payload.repository.name,
      payload.pull_request.number
    );

    console.log('=== PULL REQUEST DETAILS ===');
    console.log(`Title: ${prDetails.pullRequest.title}`);
    console.log(`Author: ${prDetails.pullRequest.user.login}`);
    console.log(`State: ${prDetails.pullRequest.state}`);
    console.log(`Created: ${prDetails.pullRequest.created_at}`);
    console.log(`Base branch: ${prDetails.pullRequest.base.ref}`);
    console.log(`Head branch: ${prDetails.pullRequest.head.ref}`);

    console.log('\n=== METRICS ===');
    console.log(`Total commits: ${prDetails.metrics.totalCommits}`);
    console.log(`Total files changed: ${prDetails.metrics.totalFiles}`);
    console.log(`Total additions: ${prDetails.metrics.totalAdditions}`);
    console.log(`Total deletions: ${prDetails.metrics.totalDeletions}`);
    console.log(`Total changes: ${prDetails.metrics.totalChanges}`);
    console.log(`File types: ${prDetails.metrics.fileTypes.join(', ')}`);
    if (prDetails.metrics.largestFile) {
      console.log(`Largest file: ${prDetails.metrics.largestFile.filename} (${prDetails.metrics.largestFile.changes} changes)`);
    }

    // Optionally add a comment with metrics
    const metricsComment = `## 📊 Pull Request Metrics

**Files & Changes:**
- 📁 Files changed: ${prDetails.metrics.totalFiles}
- ➕ Additions: ${prDetails.metrics.totalAdditions}
- ➖ Deletions: ${prDetails.metrics.totalDeletions}
- 🔄 Total changes: ${prDetails.metrics.totalChanges}

**Commits & Reviews:**
- 📝 Commits: ${prDetails.metrics.totalCommits}
- 👀 Reviews: ${prDetails.metrics.totalReviews}
- 💬 Comments: ${prDetails.metrics.totalComments}

**File Types:** ${prDetails.metrics.fileTypes.join(', ')}

${prDetails.metrics.largestFile ? `**Largest file:** ${prDetails.metrics.largestFile.filename} (${prDetails.metrics.largestFile.changes} changes)` : ''}

---
*Metrics generated automatically by GitHub App*`;

    await octokit.request("POST /repos/{owner}/{repo}/issues/{issue_number}/comments", {
      owner: payload.repository.owner.login,
      repo: payload.repository.name,
      issue_number: payload.pull_request.number,
      body: metricsComment,
      headers: {
        "x-github-api-version": "2022-11-28",
      },
    });

    console.log('✅ Posted metrics comment to PR');

  } catch (error) {
    if (error.response) {
      console.error(`Error! Status: ${error.response.status}. Message: ${error.response.data.message}`)
    }
    console.error(error)
  }
};

async function handlePRClosed({octokit, payload}) {
  console.log(`Received a pull request closed event for #${payload.pull_request.number}`);

  if (!payload.pull_request.merged) {
    console.log('PR was closed without merging');
    return;
  }

  try {
    console.log('PR was merged! Fetching final metrics...');

    // Fetch detailed PR information for the merged PR
    const prDetails = await fetchPullRequestDetails(
      octokit,
      payload.repository.owner.login,
      payload.repository.name,
      payload.pull_request.number
    );

    console.log('\n=== MERGED PULL REQUEST SUMMARY ===');
    console.log(`Title: ${prDetails.pullRequest.title}`);
    console.log(`Author: ${prDetails.pullRequest.user.login}`);
    console.log(`Merged by: ${payload.pull_request.merged_by?.login || 'Unknown'}`);
    console.log(`Merged at: ${payload.pull_request.merged_at}`);
    console.log(`Time to merge: ${calculateTimeToMerge(prDetails.pullRequest.created_at, payload.pull_request.merged_at)}`);

    console.log('\n=== FINAL METRICS ===');
    console.log(`Total commits: ${prDetails.metrics.totalCommits}`);
    console.log(`Total files changed: ${prDetails.metrics.totalFiles}`);
    console.log(`Total additions: ${prDetails.metrics.totalAdditions}`);
    console.log(`Total deletions: ${prDetails.metrics.totalDeletions}`);
    console.log(`Total reviews: ${prDetails.metrics.totalReviews}`);
    console.log(`Approved reviews: ${prDetails.metrics.approvedReviews}`);
    console.log(`Changes requested: ${prDetails.metrics.changesRequestedReviews}`);
    console.log(`Total comments: ${prDetails.metrics.totalComments}`);

    // Store or process the metrics as needed
    // This is where you could save to a database, send to analytics, etc.

  } catch (error) {
    console.error(`Error processing merged PR: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}, Message: ${error.response.data.message}`);
    }
  }
}

// Helper function to calculate time between PR creation and merge
function calculateTimeToMerge(createdAt, mergedAt) {
  const created = new Date(createdAt);
  const merged = new Date(mergedAt);
  const diffMs = merged - created;

  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}

// This sets up a webhook event listener. When your app receives a webhook event from GitHub with a `X-GitHub-Event` header value of `pull_request` and an `action` payload value of `opened`, it calls the `handlePullRequestOpened` event handler that is defined above.
app.webhooks.on("pull_request.opened", handlePullRequestOpened);
app.webhooks.on("pull_request.closed", handlePRClosed);

// This logs any errors that occur.
app.webhooks.onError((error) => {
  if (error.name === "AggregateError") {
    console.error(`Error processing request: ${error.event}`);
  } else {
    console.error(error);
  }
});

// This determines where your server will listen.
//
// For local development, your server will listen to port 3000 on `localhost`. When you deploy your app, you will change these values. For more information, see [Deploy your app](#deploy-your-app).
const port = 3000;
const host = 'localhost';
const path = "/api/webhook";
const localWebhookUrl = `http://${host}:${port}${path}`;

// This sets up a middleware function to handle incoming webhook events.
//
// Octokit's `createNodeMiddleware` function takes care of generating this middleware function for you. The resulting middleware function will:
//
//    - Check the signature of the incoming webhook event to make sure that it matches your webhook secret. This verifies that the incoming webhook event is a valid GitHub event.
//    - Parse the webhook event payload and identify the type of event.
//    - Trigger the corresponding webhook event handler.
const middleware = createNodeMiddleware(app.webhooks, {path});

// This creates a Node.js server that listens for incoming HTTP requests (including webhook payloads from GitHub) on the specified port. When the server receives a request, it executes the `middleware` function that you defined earlier. Once the server is running, it logs messages to the console to indicate that it is listening.
http.createServer(middleware).listen(port, () => {
  console.log(`Server is listening for events at: ${localWebhookUrl}`);
  console.log('Press Ctrl + C to quit.')
});
