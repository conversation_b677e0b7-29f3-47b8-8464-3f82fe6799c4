// These are the dependencies for this file.
//
// You installed the `dotenv` and `octokit` modules earlier. The `@octokit/webhooks` is a dependency of the `octokit` module, so you don't need to install it separately. The `fs` and `http` dependencies are built-in Node.js modules.
import dotenv from "dotenv";
import {App} from "octokit";
import {createNodeMiddleware} from "@octokit/webhooks";
import fs from "fs";
import http from "http";

// This reads your `.env` file and adds the variables from that file to the `process.env` object in Node.js.
dotenv.config();

// This assigns the values of your environment variables to local variables.
const appId = process.env.APP_ID;
const webhookSecret = process.env.WEBHOOK_SECRET;
const privateKeyPath = process.env.PRIVATE_KEY_PATH;

// This reads the contents of your private key file.
const privateKey = fs.readFileSync(privateKeyPath, "utf8");

// This creates a new instance of the Octokit App class.
const app = new App({
  appId: appId,
  privateKey: privateKey,
  webhooks: {
    secret: webhookSecret
  },
});

// This defines the message that your app will post to pull requests.
const messageForNewPRs = "Thanks for opening a new PR! Please follow our contributing guidelines to make your PR easier to review.";

// Abstract function to send metrics to an endpoint
async function sendMetricsToEndpoint(metricsData) {
  try {
    // Use local mock endpoint by default for testing
    const endpoint = process.env.METRICS_ENDPOINT || `http://${host}:${port}/api/metrics`;

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add authentication headers as needed
        // 'Authorization': `Bearer ${process.env.API_TOKEN}`,
      },
      body: JSON.stringify(metricsData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    console.log('✅ Metrics sent successfully to endpoint');
    return await response.json();
  } catch (error) {
    console.error('❌ Failed to send metrics to endpoint:', error.message);
    // Don't throw - we don't want to break the webhook if metrics sending fails
  }
}

// Function to create a structured metrics object
function createMetricsPayload(pullRequest, metrics, eventType, repository) {
  return {
    event: eventType,
    timestamp: new Date().toISOString(),
    repository: {
      owner: repository.owner.login,
      name: repository.name,
      fullName: repository.full_name,
      id: repository.id
    },
    pullRequest: {
      number: pullRequest.number,
      title: pullRequest.title,
      author: pullRequest.user.login,
      authorId: pullRequest.user.id,
      state: pullRequest.state,
      createdAt: pullRequest.created_at,
      updatedAt: pullRequest.updated_at,
      mergedAt: pullRequest.merged_at,
      mergedBy: pullRequest.merged_by?.login || null,
      baseBranch: pullRequest.base.ref,
      headBranch: pullRequest.head.ref,
      url: pullRequest.html_url
    },
    metrics: {
      commits: metrics.totalCommits,
      filesChanged: metrics.totalFiles,
      additions: metrics.totalAdditions,
      deletions: metrics.totalDeletions,
      totalChanges: metrics.totalChanges,
      reviews: {
        total: metrics.totalReviews,
        approved: metrics.approvedReviews,
        changesRequested: metrics.changesRequestedReviews
      },
      comments: metrics.totalComments,
      fileTypes: metrics.fileTypes,
      largestFile: metrics.largestFile ? {
        filename: metrics.largestFile.filename,
        changes: metrics.largestFile.changes,
        additions: metrics.largestFile.additions,
        deletions: metrics.largestFile.deletions
      } : null,
      timeToMerge: pullRequest.merged_at ?
        calculateTimeToMerge(pullRequest.created_at, pullRequest.merged_at) : null
    }
  };
}

// Helper function to calculate time between PR creation and merge
function calculateTimeToMerge(createdAt, mergedAt) {
  const created = new Date(createdAt);
  const merged = new Date(mergedAt);
  const diffMs = merged - created;

  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}

// GraphQL query to fetch comprehensive PR data in a single request
const PR_DETAILS_QUERY = `
  query($owner: String!, $repo: String!, $number: Int!) {
    repository(owner: $owner, name: $repo) {
      pullRequest(number: $number) {
        id
        number
        title
        body
        state
        createdAt
        updatedAt
        mergedAt
        closedAt
        merged
        mergeable
        url
        author {
          login
          ... on User {
            id
          }
        }
        mergedBy {
          login
        }
        baseRefName
        headRefName
        additions
        deletions
        changedFiles
        commits(first: 100) {
          totalCount
          nodes {
            commit {
              message
              author {
                name
                email
                date
              }
              additions
              deletions
              changedFiles
            }
          }
        }
        files(first: 100) {
          totalCount
          nodes {
            path
            additions
            deletions
            changeType
          }
        }
        reviews(first: 100) {
          totalCount
          nodes {
            id
            state
            author {
              login
            }
            createdAt
            submittedAt
          }
        }
        comments(first: 100) {
          totalCount
          nodes {
            id
            author {
              login
            }
            body
            createdAt
          }
        }
        reviewComments(first: 100) {
          totalCount
          nodes {
            id
            author {
              login
            }
            body
            createdAt
          }
        }
      }
    }
  }
`;

// Function to fetch detailed pull request information using GraphQL
async function fetchPullRequestDetails(octokit, owner, repo, pullNumber) {
  try {
    const response = await octokit.graphql(PR_DETAILS_QUERY, {
      owner,
      repo,
      number: pullNumber
    });

    const pr = response.repository.pullRequest;

    if (!pr) {
      throw new Error(`Pull request #${pullNumber} not found`);
    }

    // Extract file types from file paths
    const fileTypes = [...new Set(pr.files.nodes.map(file => {
      const ext = file.path.split('.').pop();
      return ext || 'no-extension';
    }))];

    // Find the largest file by total changes
    const largestFile = pr.files.nodes.reduce((largest, file) => {
      const changes = file.additions + file.deletions;
      return changes > (largest?.changes || 0) ? {
        filename: file.path,
        changes,
        additions: file.additions,
        deletions: file.deletions,
        changeType: file.changeType
      } : largest;
    }, null);

    // Calculate metrics
    const metrics = {
      totalCommits: pr.commits.totalCount,
      totalFiles: pr.changedFiles,
      totalAdditions: pr.additions,
      totalDeletions: pr.deletions,
      totalChanges: pr.additions + pr.deletions,
      totalReviews: pr.reviews.totalCount,
      totalComments: pr.comments.totalCount + pr.reviewComments.totalCount,
      approvedReviews: pr.reviews.nodes.filter(review => review.state === 'APPROVED').length,
      changesRequestedReviews: pr.reviews.nodes.filter(review => review.state === 'CHANGES_REQUESTED').length,
      fileTypes,
      largestFile
    };

    // Transform to match expected structure
    const pullRequest = {
      id: pr.id,
      number: pr.number,
      title: pr.title,
      body: pr.body,
      state: pr.state.toLowerCase(),
      created_at: pr.createdAt,
      updated_at: pr.updatedAt,
      merged_at: pr.mergedAt,
      closed_at: pr.closedAt,
      merged: pr.merged,
      mergeable: pr.mergeable,
      html_url: pr.url,
      user: {
        login: pr.author?.login || 'unknown',
        id: pr.author?.id || null
      },
      merged_by: pr.mergedBy ? { login: pr.mergedBy.login } : null,
      base: { ref: pr.baseRefName },
      head: { ref: pr.headRefName },
      additions: pr.additions,
      deletions: pr.deletions,
      changed_files: pr.changedFiles
    };

    return {
      pullRequest,
      commits: pr.commits.nodes,
      files: pr.files.nodes,
      reviews: pr.reviews.nodes,
      comments: [...pr.comments.nodes, ...pr.reviewComments.nodes],
      metrics
    };
  } catch (error) {
    console.error(`Error fetching PR details via GraphQL: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}, Message: ${error.response.data?.message || error.message}`);
    }
    throw error;
  }
}

// This adds an event handler that your code will call later. When this event handler is called, it will log the event to the console. Then, it will use GitHub's REST API to add a comment to the pull request that triggered the event.
async function handlePullRequestOpened({octokit, payload}) {
  console.log(`Received a pull request opened event for #${payload.pull_request.number}`);

  try {
    // Fetch detailed PR information
    const prDetails = await fetchPullRequestDetails(
      octokit,
      payload.repository.owner.login,
      payload.repository.name,
      payload.pull_request.number
    );

    // Create structured metrics payload
    const metricsPayload = createMetricsPayload(
      prDetails.pullRequest,
      prDetails.metrics,
      'pull_request.opened',
      payload.repository
    );

    // Send metrics to endpoint
    await sendMetricsToEndpoint(metricsPayload);

  } catch (error) {
    if (error.response) {
      console.error(`Error! Status: ${error.response.status}. Message: ${error.response.data.message}`)
    }
    console.error(error)
  }
};

async function handlePRClosed({octokit, payload}) {
  console.log(`Received a pull request closed event for #${payload.pull_request.number}`);

  if (!payload.pull_request.merged) {
    console.log('PR was closed without merging');
    return;
  }

  try {
    console.log('PR was merged! Fetching final metrics...');

    // Fetch detailed PR information for the merged PR
    const prDetails = await fetchPullRequestDetails(
      octokit,
      payload.repository.owner.login,
      payload.repository.name,
      payload.pull_request.number
    );

    // Create structured metrics payload for merged PR
    const metricsPayload = createMetricsPayload(
      prDetails.pullRequest,
      prDetails.metrics,
      'pull_request.merged',
      payload.repository
    );

    // Send metrics to endpoint
    await sendMetricsToEndpoint(metricsPayload);

    console.log(`✅ Processed merged PR #${payload.pull_request.number}: ${prDetails.pullRequest.title}`);

  } catch (error) {
    console.error(`Error processing merged PR: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}, Message: ${error.response.data.message}`);
    }
  }
}

// This sets up a webhook event listener. When your app receives a webhook event from GitHub with a `X-GitHub-Event` header value of `pull_request` and an `action` payload value of `opened`, it calls the `handlePullRequestOpened` event handler that is defined above.
app.webhooks.on("pull_request.opened", handlePullRequestOpened);
app.webhooks.on("pull_request.closed", handlePRClosed);

// This logs any errors that occur.
app.webhooks.onError((error) => {
  if (error.name === "AggregateError") {
    console.error(`Error processing request: ${error.event}`);
  } else {
    console.error(error);
  }
});

// This determines where your server will listen.
//
// For local development, your server will listen to port 3000 on `localhost`. When you deploy your app, you will change these values. For more information, see [Deploy your app](#deploy-your-app).
const port = 3000;
const host = 'localhost';
const path = "/api/webhook";
const localWebhookUrl = `http://${host}:${port}${path}`;

// Mock metrics endpoint handler
function handleMetricsEndpoint(req, res) {
  if (req.method !== 'POST') {
    res.writeHead(405, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Method not allowed' }));
    return;
  }

  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });

  req.on('end', () => {
    try {
      const metricsData = JSON.parse(body);

      console.log('\n🎯 === MOCK METRICS ENDPOINT RECEIVED ===');
      console.log(`Event: ${metricsData.event}`);
      console.log(`Repository: ${metricsData.repository.fullName}`);
      console.log(`PR #${metricsData.pullRequest.number}: ${metricsData.pullRequest.title}`);
      console.log(`Author: ${metricsData.pullRequest.author}`);

      console.log('\n📊 Metrics Summary:');
      console.log(`  Commits: ${metricsData.metrics.commits}`);
      console.log(`  Files Changed: ${metricsData.metrics.filesChanged}`);
      console.log(`  Additions: ${metricsData.metrics.additions}`);
      console.log(`  Deletions: ${metricsData.metrics.deletions}`);
      console.log(`  Reviews: ${metricsData.metrics.reviews.total} (${metricsData.metrics.reviews.approved} approved)`);
      console.log(`  Comments: ${metricsData.metrics.comments}`);
      console.log(`  File Types: ${metricsData.metrics.fileTypes.join(', ')}`);

      if (metricsData.metrics.largestFile) {
        console.log(`  Largest File: ${metricsData.metrics.largestFile.filename} (${metricsData.metrics.largestFile.changes} changes)`);
      }

      if (metricsData.metrics.timeToMerge) {
        console.log(`  Time to Merge: ${metricsData.metrics.timeToMerge}`);
      }

      console.log('\n📝 Full Payload:');
      console.log(JSON.stringify(metricsData, null, 2));
      console.log('='.repeat(50));

      // Simulate processing time
      setTimeout(() => {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: true,
          message: 'Metrics received successfully',
          timestamp: new Date().toISOString(),
          processedEvent: metricsData.event,
          prNumber: metricsData.pullRequest.number
        }));
      }, 100);

    } catch (error) {
      console.error('❌ Error parsing metrics payload:', error.message);
      res.writeHead(400, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Invalid JSON payload' }));
    }
  });
}

// Custom request handler that routes to different endpoints
function requestHandler(req, res) {
  const url = new URL(req.url, `http://${req.headers.host}`);

  if (url.pathname === '/api/metrics') {
    handleMetricsEndpoint(req, res);
  } else if (url.pathname === path) {
    // Handle GitHub webhooks
    middleware(req, res);
  } else if (url.pathname === '/') {
    // Simple status page
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(`
      <html>
        <head><title>GitHub PR Metrics Collector</title></head>
        <body>
          <h1>🚀 GitHub PR Metrics Collector</h1>
          <p><strong>Status:</strong> Running</p>
          <p><strong>Webhook Endpoint:</strong> <code>${localWebhookUrl}</code></p>
          <p><strong>Mock Metrics Endpoint:</strong> <code>http://${host}:${port}/api/metrics</code></p>
          <p><strong>Time:</strong> ${new Date().toISOString()}</p>

          <h2>📊 Available Endpoints:</h2>
          <ul>
            <li><code>POST ${path}</code> - GitHub webhook receiver</li>
            <li><code>POST /api/metrics</code> - Mock metrics endpoint</li>
            <li><code>GET /</code> - This status page</li>
          </ul>

          <h2>🔧 Configuration:</h2>
          <ul>
            <li><strong>App ID:</strong> ${appId ? '✅ Set' : '❌ Missing'}</li>
            <li><strong>Webhook Secret:</strong> ${webhookSecret ? '✅ Set' : '❌ Missing'}</li>
            <li><strong>Private Key:</strong> ${privateKeyPath ? '✅ Set' : '❌ Missing'}</li>
          </ul>
        </body>
      </html>
    `);
  } else {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Not found' }));
  }
}

// This sets up a middleware function to handle incoming webhook events.
//
// Octokit's `createNodeMiddleware` function takes care of generating this middleware function for you. The resulting middleware function will:
//
//    - Check the signature of the incoming webhook event to make sure that it matches your webhook secret. This verifies that the incoming webhook event is a valid GitHub event.
//    - Parse the webhook event payload and identify the type of event.
//    - Trigger the corresponding webhook event handler.
const middleware = createNodeMiddleware(app.webhooks, {path});

// This creates a Node.js server that listens for incoming HTTP requests (including webhook payloads from GitHub) on the specified port. When the server receives a request, it executes the custom request handler that routes to different endpoints.
http.createServer(requestHandler).listen(port, () => {
  console.log(`🚀 Server is running on http://${host}:${port}`);
  console.log(`📡 GitHub webhook endpoint: ${localWebhookUrl}`);
  console.log(`🎯 Mock metrics endpoint: http://${host}:${port}/api/metrics`);
  console.log(`📊 Status page: http://${host}:${port}/`);
  console.log('Press Ctrl + C to quit.')
});
