# GitHub Pull Request Metrics Collector

A GitHub App that automatically fetches detailed pull request information and metrics when PRs are opened or merged.

## Features

- **Automatic PR Analysis**: Automatically analyzes PRs when they are opened or merged
- **Comprehensive Metrics**: Collects detailed metrics including:
  - Number of commits, files changed, additions/deletions
  - Code review information (approvals, change requests)
  - Comments and discussion metrics
  - File types and largest files changed
  - Time to merge calculations
- **Webhook Integration**: Responds to GitHub webhook events
- **Manual PR Fetching**: Programmatically fetch details for any PR

## Setup

1. **Environment Variables**: Create a `.env` file with:
   ```
   APP_ID=your_github_app_id
   WEBHOOK_SECRET=your_webhook_secret
   PRIVATE_KEY_PATH=path_to_your_private_key.pem
   ```

2. **Install Dependencies**:
   ```bash
   npm install
   ```

3. **Start the Server**:
   ```bash
   npm start
   ```

## Usage

### Webhook Events

The app automatically responds to:
- `pull_request.opened` - Analyzes new PRs and posts metrics comment
- `pull_request.closed` - Logs final metrics for merged PRs

### Manual PR Analysis

You can fetch PR details programmatically:

```javascript
import { fetchPullRequestDetails } from './src/example-usage.js';

// Get PR details
const prDetails = await fetchPullRequestDetails(octokit, owner, repo, pullNumber);

console.log('PR Metrics:', prDetails.metrics);
```

## Metrics Collected

### Basic Information
- PR title, author, state, creation/merge dates
- Base and head branches
- Merge information (if applicable)

### Code Metrics
- **Commits**: Total number of commits in the PR
- **Files**: Number of files changed
- **Additions/Deletions**: Lines of code added/removed
- **File Types**: Programming languages/file extensions involved
- **Largest File**: File with the most changes

### Review Metrics
- **Reviews**: Total number of reviews
- **Approvals**: Number of approved reviews
- **Change Requests**: Number of reviews requesting changes
- **Comments**: Total discussion comments

### Time Metrics
- **Time to Merge**: Duration from PR creation to merge

## Example Output

When a PR is opened, the app will:

1. **Log to Console**:
   ```
   === PULL REQUEST DETAILS ===
   Title: Add new feature for user authentication
   Author: developer123
   State: open
   Created: 2024-01-15T10:30:00Z
   Base branch: main
   Head branch: feature/auth

   === METRICS ===
   Total commits: 5
   Total files changed: 12
   Total additions: 245
   Total deletions: 67
   File types: js, ts, json, md
   Largest file: src/auth.js (89 changes)
   ```

2. **Post Comment to PR**:
   ```markdown
   ## 📊 Pull Request Metrics

   **Files & Changes:**
   - 📁 Files changed: 12
   - ➕ Additions: 245
   - ➖ Deletions: 67
   - 🔄 Total changes: 312

   **Commits & Reviews:**
   - 📝 Commits: 5
   - 👀 Reviews: 2
   - 💬 Comments: 8

   **File Types:** js, ts, json, md
   **Largest file:** src/auth.js (89 changes)
   ```

## API Reference

### `fetchPullRequestDetails(octokit, owner, repo, pullNumber)`

Fetches comprehensive PR details and metrics.

**Parameters:**
- `octokit`: Authenticated Octokit instance
- `owner`: Repository owner
- `repo`: Repository name
- `pullNumber`: PR number

**Returns:**
```javascript
{
  pullRequest: { /* GitHub PR object */ },
  commits: [ /* Array of commit objects */ ],
  files: [ /* Array of changed files */ ],
  reviews: [ /* Array of reviews */ ],
  comments: [ /* Array of comments */ ],
  metrics: {
    totalCommits: number,
    totalFiles: number,
    totalAdditions: number,
    totalDeletions: number,
    totalChanges: number,
    totalReviews: number,
    totalComments: number,
    approvedReviews: number,
    changesRequestedReviews: number,
    fileTypes: string[],
    largestFile: object
  }
}
```

## Configuration

The app can be customized by modifying:
- **Comment Template**: Edit the `metricsComment` in `handlePullRequestOpened`
- **Metrics Calculation**: Modify the metrics object in `fetchPullRequestDetails`
- **Event Handlers**: Add more webhook event listeners as needed

## Development

To extend the functionality:

1. **Add New Metrics**: Modify the `fetchPullRequestDetails` function
2. **Handle More Events**: Add new webhook handlers
3. **Custom Processing**: Extend the event handlers to save data, send notifications, etc.

## Troubleshooting

- **Authentication Issues**: Verify your GitHub App credentials and permissions
- **Webhook Delivery**: Check that your webhook URL is accessible and the secret matches
- **Rate Limits**: The app includes error handling for GitHub API rate limits

## License

MIT License
