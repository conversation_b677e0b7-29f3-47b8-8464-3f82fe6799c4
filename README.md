# GitHub Pull Request Metrics Collector

A GitHub App that automatically fetches detailed pull request information and metrics when PRs are opened or merged.

## Features

- **Automatic PR Analysis**: Automatically analyzes PRs when they are opened, closed, or merged
- **GraphQL Efficiency**: Uses GitHub's GraphQL API for efficient single-request data fetching
- **Metrics API Integration**: Sends structured metrics data to your configured endpoint
- **Comprehensive Metrics**: Collects detailed metrics including:
  - Number of commits, files changed, additions/deletions
  - Code review information (approvals, change requests)
  - Comments and discussion metrics (including review comments)
  - File types and largest files changed
  - Time to merge calculations
- **Webhook Integration**: Responds to GitHub webhook events
- **Configurable Comments**: Optional PR comments with metrics (can be disabled)
- **Manual PR Fetching**: Programmatically fetch details for any PR

## Setup

1. **Environment Variables**: Create a `.env` file with:
   ```
   APP_ID=your_github_app_id
   WEBHOOK_SECRET=your_webhook_secret
   PRIVATE_KEY_PATH=path_to_your_private_key.pem
   METRICS_ENDPOINT=https://your-metrics-api.com/webhook
   POST_METRICS_COMMENT=true
   ```

2. **Install Dependencies**:
   ```bash
   npm install
   ```

3. **Start the Server**:
   ```bash
   # For production (uses compiled dist/index.js)
   npm start

   # For development (uses src/index.js directly)
   npm run dev
   ```

## Usage

### Local Development & Testing

The server includes a built-in mock metrics endpoint for testing:

1. **Start the server**: `npm run dev`
2. **Visit the status page**: http://localhost:3000
3. **Test the mock endpoint**: `npm run test-metrics`

Available endpoints:
- `GET /` - Status page with configuration info
- `POST /api/webhook` - GitHub webhook receiver
- `POST /api/metrics` - Mock metrics endpoint (for testing)

### Webhook Events

The app automatically responds to:
- `pull_request.opened` - Analyzes new PRs, sends metrics to endpoint, optionally posts comment
- `pull_request.closed` - Sends final metrics for merged/closed PRs to endpoint

### Manual PR Analysis

You can fetch PR details programmatically:

```javascript
import { fetchPullRequestDetails } from './src/example-usage.js';

// Get PR details
const prDetails = await fetchPullRequestDetails(octokit, owner, repo, pullNumber);

console.log('PR Metrics:', prDetails.metrics);
```

## Metrics Collected

### Basic Information
- PR title, author, state, creation/merge dates
- Base and head branches
- Merge information (if applicable)

### Code Metrics
- **Commits**: Total number of commits in the PR
- **Files**: Number of files changed
- **Additions/Deletions**: Lines of code added/removed
- **File Types**: Programming languages/file extensions involved
- **Largest File**: File with the most changes

### Review Metrics
- **Reviews**: Total number of reviews
- **Approvals**: Number of approved reviews
- **Change Requests**: Number of reviews requesting changes
- **Comments**: Total discussion comments

### Time Metrics
- **Time to Merge**: Duration from PR creation to merge

## Metrics Payload Structure

The app sends a structured JSON payload to your configured endpoint:

```json
{
  "event": "pull_request.opened",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "repository": {
    "owner": "your-org",
    "name": "your-repo",
    "fullName": "your-org/your-repo",
    "id": 123456789
  },
  "pullRequest": {
    "number": 42,
    "title": "Add new feature for user authentication",
    "author": "developer123",
    "authorId": 987654321,
    "state": "open",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T11:45:00Z",
    "mergedAt": null,
    "mergedBy": null,
    "baseBranch": "main",
    "headBranch": "feature/auth",
    "url": "https://github.com/your-org/your-repo/pull/42"
  },
  "metrics": {
    "commits": 5,
    "filesChanged": 12,
    "additions": 245,
    "deletions": 67,
    "totalChanges": 312,
    "reviews": {
      "total": 2,
      "approved": 1,
      "changesRequested": 0
    },
    "comments": 8,
    "fileTypes": ["js", "ts", "json", "md"],
    "largestFile": {
      "filename": "src/auth.js",
      "changes": 89,
      "additions": 76,
      "deletions": 13
    },
    "timeToMerge": null
  }
}
```

## Example Output

When a PR is opened, the app will:

1. **Send Metrics to Endpoint**: POST the structured JSON payload to your configured endpoint

2. **Log to Console**:
   ```
   Received a pull request opened event for #42
   ✅ Metrics sent successfully to endpoint
   ✅ Posted metrics comment to PR
   ```

3. **Post Comment to PR** (if enabled):
   ```markdown
   ## 📊 Pull Request Metrics

   **Files & Changes:**
   - 📁 Files changed: 12
   - ➕ Additions: 245
   - ➖ Deletions: 67
   - 🔄 Total changes: 312

   **Commits & Reviews:**
   - 📝 Commits: 5
   - 👀 Reviews: 2
   - 💬 Comments: 8

   **File Types:** js, ts, json, md
   **Largest file:** src/auth.js (89 changes)
   ```

## API Reference

### `fetchPullRequestDetails(octokit, owner, repo, pullNumber)`

Fetches comprehensive PR details and metrics using a single GraphQL query for optimal performance.

**Parameters:**
- `octokit`: Authenticated Octokit instance
- `owner`: Repository owner
- `repo`: Repository name
- `pullNumber`: PR number

**Returns:**
```javascript
{
  pullRequest: { /* GitHub PR object */ },
  commits: [ /* Array of commit objects */ ],
  files: [ /* Array of changed files */ ],
  reviews: [ /* Array of reviews */ ],
  comments: [ /* Array of comments */ ],
  metrics: {
    totalCommits: number,
    totalFiles: number,
    totalAdditions: number,
    totalDeletions: number,
    totalChanges: number,
    totalReviews: number,
    totalComments: number,
    approvedReviews: number,
    changesRequestedReviews: number,
    fileTypes: string[],
    largestFile: object
  }
}
```

## GraphQL Advantages

This implementation uses GitHub's GraphQL API instead of multiple REST API calls:

- **Single Request**: Fetches all PR data (commits, files, reviews, comments) in one query
- **Reduced Rate Limiting**: Uses fewer API calls, preserving your rate limit
- **Better Performance**: Faster response times with less network overhead
- **Precise Data**: Only requests the fields we actually need
- **Unified Comments**: Combines both issue comments and review comments

The GraphQL query fetches:
- Pull request metadata and state
- All commits with author information
- All changed files with additions/deletions
- All reviews with states and authors
- All comments (both issue and review comments)

## Configuration

The app can be customized by modifying:
- **GraphQL Query**: Modify `PR_DETAILS_QUERY` to fetch additional fields
- **Metrics Calculation**: Modify the metrics object in `fetchPullRequestDetails`
- **Event Handlers**: Add more webhook event listeners as needed

## Development

To extend the functionality:

1. **Add New Metrics**: Modify the `fetchPullRequestDetails` function
2. **Handle More Events**: Add new webhook handlers
3. **Custom Processing**: Extend the event handlers to save data, send notifications, etc.

## Troubleshooting

- **Authentication Issues**: Verify your GitHub App credentials and permissions
- **Webhook Delivery**: Check that your webhook URL is accessible and the secret matches
- **Rate Limits**: The app includes error handling for GitHub API rate limits

## License

MIT License
